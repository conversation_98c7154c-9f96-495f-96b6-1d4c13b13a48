import { defineComponent as g, inject as a, computed as i, openBlock as t, createElementBlock as e, mergeProps as H, renderSlot as V, createCommentVNode as Z, createElementVNode as o } from "vue";
const S = ["width", "height", "fill", "transform"], M = { key: 0 }, C = /* @__PURE__ */ o("path", { d: "M216,36H40A20,20,0,0,0,20,56V200a20,20,0,0,0,20,20H216a20,20,0,0,0,20-20V56A20,20,0,0,0,216,36Zm-4,160H44V60H212ZM81.43,166.05C94,175.05,110.56,180,128,180s34-4.95,46.57-13.95C188.19,156.32,196,142.45,196,128s-7.81-28.32-21.43-38C162,81,145.44,76,128,76S94,81,81.43,90C67.81,99.68,60,113.55,60,128S67.81,156.32,81.43,166.05ZM128,100c23.85,0,44,12.82,44,28s-20.15,28-44,28-44-12.82-44-28S104.15,100,128,100Z" }, null, -1), v = [
  C
], y = { key: 1 }, A = /* @__PURE__ */ o("path", {
  d: "M216,48H40a8,8,0,0,0-8,8V200a8,8,0,0,0,8,8H216a8,8,0,0,0,8-8V56A8,8,0,0,0,216,48ZM128,176c-35.35,0-64-21.49-64-48s28.65-48,64-48,64,21.49,64,48S163.35,176,128,176Z",
  opacity: "0.2"
}, null, -1), f = /* @__PURE__ */ o("path", { d: "M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM178.05,87.66C164.59,77.56,146.81,72,128,72S91.41,77.56,78,87.66C63.79,98.27,56,112.6,56,128s7.79,29.73,22,40.34C91.41,178.44,109.19,184,128,184s36.59-5.56,50.05-15.66C192.21,157.73,200,143.4,200,128S192.21,98.27,178.05,87.66ZM128,168c-30.88,0-56-17.94-56-40s25.12-40,56-40,56,17.94,56,40S158.88,168,128,168Z" }, null, -1), w = [
  A,
  f
], k = { key: 2 }, x = /* @__PURE__ */ o("path", { d: "M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm-16,88c0,30.93-32.24,56-72,56s-72-25.07-72-56,32.24-56,72-56S200,97.07,200,128Z" }, null, -1), z = [
  x
], B = { key: 3 }, N = /* @__PURE__ */ o("path", { d: "M216,42H40A14,14,0,0,0,26,56V200a14,14,0,0,0,14,14H216a14,14,0,0,0,14-14V56A14,14,0,0,0,216,42Zm2,158a2,2,0,0,1-2,2H40a2,2,0,0,1-2-2V56a2,2,0,0,1,2-2H216a2,2,0,0,1,2,2ZM128,74c-38.6,0-70,24.22-70,54s31.4,54,70,54,70-24.22,70-54S166.6,74,128,74Zm0,96c-32,0-58-18.84-58-42s26-42,58-42,58,18.84,58,42S160,170,128,170Z" }, null, -1), b = [
  N
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM178.05,87.66C164.59,77.56,146.81,72,128,72S91.41,77.56,78,87.66C63.79,98.27,56,112.6,56,128s7.79,29.73,22,40.34C91.41,178.44,109.19,184,128,184s36.59-5.56,50.05-15.66C192.21,157.73,200,143.4,200,128S192.21,98.27,178.05,87.66ZM128,168c-30.88,0-56-17.94-56-40s25.12-40,56-40,56,17.94,56,40S158.88,168,128,168Z" }, null, -1), W = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M216,44H40A12,12,0,0,0,28,56V200a12,12,0,0,0,12,12H216a12,12,0,0,0,12-12V56A12,12,0,0,0,216,44Zm4,156a4,4,0,0,1-4,4H40a4,4,0,0,1-4-4V56a4,4,0,0,1,4-4H216a4,4,0,0,1,4,4ZM128,76c-37.5,0-68,23.33-68,52s30.5,52,68,52,68-23.33,68-52S165.5,76,128,76Zm0,96c-33.08,0-60-19.74-60-44s26.92-44,60-44,60,19.74,60,44S161.08,172,128,172Z" }, null, -1), q = [
  j
], D = {
  name: "PhVignette"
}, I = /* @__PURE__ */ g({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(r) {
    const n = r, d = a("weight", "regular"), _ = a("size", "1em"), h = a("color", "currentColor"), u = a("mirrored", !1), s = i(() => n.weight ?? d), c = i(() => n.size ?? _), m = i(() => n.color ?? h), p = i(() => n.mirrored !== void 0 ? n.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (l, F) => (t(), e("svg", H({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: c.value,
      height: c.value,
      fill: m.value,
      transform: p.value
    }, l.$attrs), [
      V(l.$slots, "default"),
      s.value === "bold" ? (t(), e("g", M, v)) : s.value === "duotone" ? (t(), e("g", y, w)) : s.value === "fill" ? (t(), e("g", k, z)) : s.value === "light" ? (t(), e("g", B, b)) : s.value === "regular" ? (t(), e("g", E, W)) : s.value === "thin" ? (t(), e("g", $, q)) : Z("", !0)
    ], 16, S));
  }
});
export {
  I as default
};
