import { defineComponent as m, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as v, renderSlot as y, createCommentVNode as f, createElementVNode as o } from "vue";
const w = ["width", "height", "fill", "transform"], M = { key: 0 }, k = /* @__PURE__ */ o("path", { d: "M225.35,133.1c-15.22,18.93-30.43,29-46.5,30.65A46.71,46.71,0,0,1,174,164c-20.81,0-38.16-14.13-53.59-26.7-14.24-11.6-27.68-22.54-40.75-21.18-9.26,1-19.46,8.32-30.32,21.82a12,12,0,0,1-18.7-15C45.87,104,61.08,94,77.15,92.25c23-2.42,41.82,12.92,58.43,26.45,14.24,11.6,27.68,22.54,40.75,21.18,9.26-1,19.46-8.32,30.32-21.82a12,12,0,1,1,18.7,15Z" }, null, -1), C = [
  k
], Z = { key: 1 }, S = /* @__PURE__ */ o("path", {
  d: "M216,56V200a16,16,0,0,1-16,16H56a16,16,0,0,1-16-16V56A16,16,0,0,1,56,40H200A16,16,0,0,1,216,56Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M222.23,130.59c-14.51,18-28.84,27.6-43.8,29.17a43,43,0,0,1-4.5.24c-19.3,0-35.39-13.1-51-25.8-14.91-12.14-29-23.61-43.7-22-10.51,1.1-21.31,8.72-33,23.28a8,8,0,0,1-12.46-10c14.51-18,28.84-27.6,43.8-29.17,21.32-2.25,38.69,11.89,55.48,25.56,14.91,12.14,29,23.62,43.7,22,10.51-1.1,21.31-8.72,33-23.28a8,8,0,1,1,12.46,10Z" }, null, -1), A = [
  S,
  x
], V = { key: 2 }, z = /* @__PURE__ */ o("path", { d: "M208,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32Zm-10,99.66c-13.19,15-25.34,20.29-36.37,20.29-14.94,0-27.81-9.61-38.43-17.54-19.2-14.34-31.89-23.81-53.2.48a8,8,0,1,1-12-10.55c31.05-35.41,56.34-16.53,74.8-2.75,19.2,14.34,31.89,23.81,53.2-.48a8,8,0,1,1,12,10.55Z" }, null, -1), B = [
  z
], H = { key: 3 }, N = /* @__PURE__ */ o("path", { d: "M220.68,129.34c-14.17,17.62-28.06,26.92-42.46,28.44A40.75,40.75,0,0,1,174,158c-18.64,0-34.44-12.87-49.76-25.35S94.57,108.51,79,110.16c-11.06,1.16-22.3,9-34.36,24a6,6,0,1,1-9.36-7.52c14.17-17.61,28.06-26.92,42.46-28.43,20.52-2.18,37.54,11.7,54,25.12C147,135.76,161.42,147.48,177,145.84c11.06-1.16,22.3-9,34.36-24a6,6,0,0,1,9.36,7.52Z" }, null, -1), b = [
  N
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M222.23,130.59c-14.51,18-28.84,27.6-43.8,29.17a43,43,0,0,1-4.5.24c-19.3,0-35.39-13.1-51-25.8-14.91-12.14-29-23.61-43.7-22-10.51,1.1-21.31,8.72-33,23.28a8,8,0,0,1-12.46-10c14.51-18,28.84-27.6,43.8-29.17,21.32-2.25,38.69,11.89,55.48,25.56,14.91,12.14,29,23.62,43.7,22,10.51-1.1,21.31-8.72,33-23.28a8,8,0,1,1,12.46,10Z" }, null, -1), W = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M219.12,128.09c-13.82,17.18-27.26,26.24-41.11,27.7a38.9,38.9,0,0,1-4,.21c-18,0-33.48-12.64-48.53-24.9-15.57-12.68-30.29-24.66-46.64-22.93-11.62,1.22-23.3,9.32-35.71,24.76a4,4,0,0,1-6.24-5C50.7,110.73,64.14,101.67,78,100.21c19.67-2.08,36.38,11.53,52.54,24.69,15.57,12.68,30.29,24.66,46.64,22.93,11.62-1.22,23.3-9.32,35.71-24.76a4,4,0,1,1,6.24,5Z" }, null, -1), T = [
  j
], q = {
  name: "PhTilde"
}, G = /* @__PURE__ */ m({
  ...q,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(r) {
    const c = r, d = n("weight", "regular"), _ = n("size", "1em"), h = n("color", "currentColor"), u = n("mirrored", !1), s = i(() => c.weight ?? d), a = i(() => c.size ?? _), p = i(() => c.color ?? h), g = i(() => c.mirrored !== void 0 ? c.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (l, D) => (t(), e("svg", v({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: a.value,
      height: a.value,
      fill: p.value,
      transform: g.value
    }, l.$attrs), [
      y(l.$slots, "default"),
      s.value === "bold" ? (t(), e("g", M, C)) : s.value === "duotone" ? (t(), e("g", Z, A)) : s.value === "fill" ? (t(), e("g", V, B)) : s.value === "light" ? (t(), e("g", H, b)) : s.value === "regular" ? (t(), e("g", E, W)) : s.value === "thin" ? (t(), e("g", $, T)) : f("", !0)
    ], 16, w));
  }
});
export {
  G as default
};
