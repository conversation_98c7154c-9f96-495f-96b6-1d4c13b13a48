import { defineComponent as m, inject as l, computed as n, openBlock as t, createElementBlock as e, mergeProps as H, renderSlot as g, createCommentVNode as A, createElementVNode as o } from "vue";
const Z = ["width", "height", "fill", "transform"], v = { key: 0 }, y = /* @__PURE__ */ o("path", { d: "M249.45,69.31a12,12,0,0,0-12.51,1L212,88.43V72a20,20,0,0,0-20-20H32A20,20,0,0,0,12,72V184a20,20,0,0,0,20,20H192a20,20,0,0,0,20-20V167.57l24.94,18.14A12,12,0,0,0,256,176V80A12,12,0,0,0,249.45,69.31ZM188,180H36V76H188Zm44-27.57-20-14.54V118.11l20-14.54Z" }, null, -1), M = [
  y
], f = { key: 1 }, w = /* @__PURE__ */ o("path", {
  d: "M200,72V184a8,8,0,0,1-8,8H32a8,8,0,0,1-8-8V72a8,8,0,0,1,8-8H192A8,8,0,0,1,200,72Z",
  opacity: "0.2"
}, null, -1), L = /* @__PURE__ */ o("path", { d: "M251.77,73a8,8,0,0,0-8.21.39L208,97.05V72a16,16,0,0,0-16-16H32A16,16,0,0,0,16,72V184a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V159l35.56,23.71A8,8,0,0,0,248,184a8,8,0,0,0,8-8V80A8,8,0,0,0,251.77,73ZM192,184H32V72H192V184Zm48-22.95-32-21.33V116.28L240,95Z" }, null, -1), k = [
  w,
  L
], x = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M192,72V184a16,16,0,0,1-16,16H32a16,16,0,0,1-16-16V72A16,16,0,0,1,32,56H176A16,16,0,0,1,192,72Zm58,.25a8.23,8.23,0,0,0-6.63,1.22L209.78,95.86A4,4,0,0,0,208,99.19v57.62a4,4,0,0,0,1.78,3.33l33.78,22.52a8,8,0,0,0,8.58.19,8.33,8.33,0,0,0,3.86-7.17V80A8,8,0,0,0,250,72.25Z" }, null, -1), S = [
  C
], z = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M250.83,74.71a6,6,0,0,0-6.16.3L206,100.79V72a14,14,0,0,0-14-14H32A14,14,0,0,0,18,72V184a14,14,0,0,0,14,14H192a14,14,0,0,0,14-14V155.21L244.67,181a6,6,0,0,0,9.33-5V80A6,6,0,0,0,250.83,74.71ZM194,184a2,2,0,0,1-2,2H32a2,2,0,0,1-2-2V72a2,2,0,0,1,2-2H192a2,2,0,0,1,2,2Zm48-19.21-36-24V115.21l36-24Z" }, null, -1), N = [
  B
], b = { key: 4 }, E = /* @__PURE__ */ o("path", { d: "M251.77,73a8,8,0,0,0-8.21.39L208,97.05V72a16,16,0,0,0-16-16H32A16,16,0,0,0,16,72V184a16,16,0,0,0,16,16H192a16,16,0,0,0,16-16V159l35.56,23.71A8,8,0,0,0,248,184a8,8,0,0,0,8-8V80A8,8,0,0,0,251.77,73ZM192,184H32V72H192V184Zm48-22.95-32-21.33V116.28L240,95Z" }, null, -1), P = [
  E
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M249.89,76.47a4,4,0,0,0-4.11.2L204,104.53V72a12,12,0,0,0-12-12H32A12,12,0,0,0,20,72V184a12,12,0,0,0,12,12H192a12,12,0,0,0,12-12V151.47l41.78,27.86A4,4,0,0,0,252,176V80A4,4,0,0,0,249.89,76.47ZM196,184a4,4,0,0,1-4,4H32a4,4,0,0,1-4-4V72a4,4,0,0,1,4-4H192a4,4,0,0,1,4,4Zm48-15.47-40-26.67V114.14l40-26.67Z" }, null, -1), j = [
  $
], q = {
  name: "PhVideoCamera"
}, G = /* @__PURE__ */ m({
  ...q,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const s = d, c = l("weight", "regular"), _ = l("size", "1em"), h = l("color", "currentColor"), V = l("mirrored", !1), a = n(() => s.weight ?? c), i = n(() => s.size ?? _), u = n(() => s.color ?? h), p = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : V ? "scale(-1, 1)" : void 0);
    return (r, D) => (t(), e("svg", H({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      g(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", v, M)) : a.value === "duotone" ? (t(), e("g", f, k)) : a.value === "fill" ? (t(), e("g", x, S)) : a.value === "light" ? (t(), e("g", z, N)) : a.value === "regular" ? (t(), e("g", b, P)) : a.value === "thin" ? (t(), e("g", W, j)) : A("", !0)
    ], 16, Z));
  }
});
export {
  G as default
};
