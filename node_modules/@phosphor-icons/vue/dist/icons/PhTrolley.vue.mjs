import { defineComponent as g, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as A, renderSlot as H, createCommentVNode as Z, createElementVNode as o } from "vue";
const v = ["width", "height", "fill", "transform"], y = { key: 0 }, V = /* @__PURE__ */ o("path", { d: "M92,224a20,20,0,1,1-20-20A20,20,0,0,1,92,224Zm124-20a20,20,0,1,0,20,20A20,20,0,0,0,216,204Zm24-36H60V75.31a19.86,19.86,0,0,0-5.86-14.14L32.49,39.51a12,12,0,0,0-17,17L36,77v91H32a12,12,0,0,0,0,24H240a12,12,0,0,0,0-24Z" }, null, -1), f = [
  V
], L = { key: 1 }, w = /* @__PURE__ */ o("path", {
  d: "M240,88v96H48V72H224A16,16,0,0,1,240,88Z",
  opacity: "0.2"
}, null, -1), M = /* @__PURE__ */ o("path", { d: "M88,224a16,16,0,1,1-16-16A16,16,0,0,1,88,224Zm128-16a16,16,0,1,0,16,16A16,16,0,0,0,216,208Zm24-32H56V75.31A15.86,15.86,0,0,0,51.31,64L29.66,42.34A8,8,0,0,0,18.34,53.66L40,75.31V176H32a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16Z" }, null, -1), k = [
  w,
  M
], x = { key: 2 }, S = /* @__PURE__ */ o("path", { d: "M88,224a16,16,0,1,1-16-16A16,16,0,0,1,88,224Zm128-16a16,16,0,1,0,16,16A16,16,0,0,0,216,208Zm24-32H56V75.31A15.86,15.86,0,0,0,51.31,64L29.66,42.34A8,8,0,0,0,18.34,53.66L40,75.31V176H32a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM88,160H216a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H88A16,16,0,0,0,72,80v64A16,16,0,0,0,88,160Z" }, null, -1), z = [
  S
], C = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M86,224a14,14,0,1,1-14-14A14,14,0,0,1,86,224Zm130-14a14,14,0,1,0,14,14A14,14,0,0,0,216,210Zm24-32H54V75.31a13.94,13.94,0,0,0-4.1-9.9L28.24,43.76a6,6,0,1,0-8.48,8.48L41.41,73.9A2,2,0,0,1,42,75.31V178H32a6,6,0,0,0,0,12H240a6,6,0,0,0,0-12Z" }, null, -1), N = [
  B
], b = { key: 4 }, E = /* @__PURE__ */ o("path", { d: "M88,224a16,16,0,1,1-16-16A16,16,0,0,1,88,224Zm128-16a16,16,0,1,0,16,16A16,16,0,0,0,216,208Zm24-32H56V75.31A15.86,15.86,0,0,0,51.31,64L29.66,42.34A8,8,0,0,0,18.34,53.66L40,75.31V176H32a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16Z" }, null, -1), P = [
  E
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M84,224a12,12,0,1,1-12-12A12,12,0,0,1,84,224Zm132-12a12,12,0,1,0,12,12A12,12,0,0,0,216,212Zm24-32H52V75.31a12,12,0,0,0-3.51-8.48L26.83,45.17a4,4,0,0,0-5.66,5.66L42.83,72.48A4,4,0,0,1,44,75.31V180H32a4,4,0,0,0,0,8H240a4,4,0,0,0,0-8Z" }, null, -1), j = [
  $
], T = {
  name: "PhTrolley"
}, F = /* @__PURE__ */ g({
  ...T,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const a = d, c = n("weight", "regular"), _ = n("size", "1em"), h = n("color", "currentColor"), m = n("mirrored", !1), s = i(() => a.weight ?? c), l = i(() => a.size ?? _), u = i(() => a.color ?? h), p = i(() => a.mirrored !== void 0 ? a.mirrored ? "scale(-1, 1)" : void 0 : m ? "scale(-1, 1)" : void 0);
    return (r, q) => (t(), e("svg", A({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      H(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", y, f)) : s.value === "duotone" ? (t(), e("g", L, k)) : s.value === "fill" ? (t(), e("g", x, z)) : s.value === "light" ? (t(), e("g", C, N)) : s.value === "regular" ? (t(), e("g", b, P)) : s.value === "thin" ? (t(), e("g", W, j)) : Z("", !0)
    ], 16, v));
  }
});
export {
  F as default
};
