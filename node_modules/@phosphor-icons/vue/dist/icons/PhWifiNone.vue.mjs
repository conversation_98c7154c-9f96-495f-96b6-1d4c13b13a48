import { defineComponent as m, inject as i, computed as l, openBlock as t, createElementBlock as e, mergeProps as v, renderSlot as y, createCommentVNode as f, createElementVNode as s } from "vue";
const A = ["width", "height", "fill", "transform"], w = { key: 0 }, M = /* @__PURE__ */ s("path", { d: "M144,204a16,16,0,1,1-16-16A16,16,0,0,1,144,204Z" }, null, -1), k = [
  M
], Z = { key: 1 }, x = /* @__PURE__ */ s("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91ZM128,208,24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), S = [
  x
], z = { key: 2 }, C = /* @__PURE__ */ s("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91ZM128,208,24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), B = [
  C
], N = { key: 3 }, W = /* @__PURE__ */ s("path", { d: "M138,204a10,10,0,1,1-10-10A10,10,0,0,1,138,204Z" }, null, -1), b = [
  W
], E = { key: 4 }, P = /* @__PURE__ */ s("path", { d: "M140,204a12,12,0,1,1-12-12A12,12,0,0,1,140,204Z" }, null, -1), V = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ s("path", { d: "M136,204a8,8,0,1,1-8-8A8,8,0,0,1,136,204Z" }, null, -1), q = [
  j
], D = {
  name: "PhWifiNone"
}, H = /* @__PURE__ */ m({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const n = c, a = i("weight", "regular"), h = i("size", "1em"), _ = i("color", "currentColor"), u = i("mirrored", !1), o = l(() => n.weight ?? a), r = l(() => n.size ?? h), p = l(() => n.color ?? _), g = l(() => n.mirrored !== void 0 ? n.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (d, F) => (t(), e("svg", v({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: r.value,
      height: r.value,
      fill: p.value,
      transform: g.value
    }, d.$attrs), [
      y(d.$slots, "default"),
      o.value === "bold" ? (t(), e("g", w, k)) : o.value === "duotone" ? (t(), e("g", Z, S)) : o.value === "fill" ? (t(), e("g", z, B)) : o.value === "light" ? (t(), e("g", N, b)) : o.value === "regular" ? (t(), e("g", E, V)) : o.value === "thin" ? (t(), e("g", $, q)) : f("", !0)
    ], 16, A));
  }
});
export {
  H as default
};
