import { defineComponent as m, inject as l, computed as n, openBlock as t, createElementBlock as e, mergeProps as g, renderSlot as Z, createCommentVNode as M, createElementVNode as o } from "vue";
const v = ["width", "height", "fill", "transform"], y = { key: 0 }, L = /* @__PURE__ */ o("path", { d: "M216.88,207.93a12,12,0,1,1-17.76,16.14L147.78,167.6a56.06,56.06,0,0,0-52.71,7.1,12,12,0,0,1-14.14-19.4,79.35,79.35,0,0,1,41.92-15.12L103.51,118.9a104.18,104.18,0,0,0-40.06,19.55,12,12,0,0,1-14.9-18.81A128.46,128.46,0,0,1,85.61,99.21l-17.31-19a151.14,151.14,0,0,0-36.68,22.28A12,12,0,1,1,16.39,83.91a175.52,175.52,0,0,1,35-22.38L39.12,48.07A12,12,0,1,1,56.88,31.93ZM128,188a16,16,0,1,0,16,16A16,16,0,0,0,128,188Zm64.55-49.55a12,12,0,0,0,14.9-18.81A127.27,127.27,0,0,0,170,99.05a12,12,0,0,0-7.87,22.67A103.62,103.62,0,0,1,192.55,138.45Zm47.06-54.54A176.33,176.33,0,0,0,128,44c-3.94,0-7.93.13-11.86.39a12,12,0,1,0,1.59,24c3.4-.23,6.86-.34,10.27-.34a152.24,152.24,0,0,1,96.38,34.46,12,12,0,1,0,15.23-18.55Z" }, null, -1), f = [
  L
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M238.2,87.69l-104,125.43a8,8,0,0,1-12.3,0L17.8,87.69a7.79,7.79,0,0,1,1.31-11.21,180.75,180.75,0,0,1,217.78,0A7.79,7.79,0,0,1,238.2,87.69Z",
  opacity: "0.2"
}, null, -1), S = /* @__PURE__ */ o("path", { d: "M53.92,34.62A8,8,0,1,0,42.08,45.38l4.55,5A187.93,187.93,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77l28.1-33.88,33.67,37a8,8,0,1,0,11.84-10.76ZM128,208,24.09,82.74A171.5,171.5,0,0,1,57.91,62.79l99.63,109.6ZM244.35,92.8l-49.42,59.58a8,8,0,0,1-12.32-10.21l49.3-59.43A170.76,170.76,0,0,0,128,48a175.15,175.15,0,0,0-32.39,3,8,8,0,1,1-3-15.72A190.62,190.62,0,0,1,128,32,186.67,186.67,0,0,1,241.72,70.1a15.93,15.93,0,0,1,6.17,10.81A15.65,15.65,0,0,1,244.35,92.8Z" }, null, -1), x = [
  k,
  S
], z = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M213.92,210.62a8,8,0,1,1-11.84,10.76l-33.67-37-28.1,33.88A15.93,15.93,0,0,1,128,224h0a15.93,15.93,0,0,1-12.31-5.77L11.65,92.8A15.65,15.65,0,0,1,8.11,80.91,15.93,15.93,0,0,1,14.28,70.1,188.26,188.26,0,0,1,46.6,50.35l-4.29-4.72a8.22,8.22,0,0,1,.13-11.38,8,8,0,0,1,11.48.37Zm34-129.71a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32a191,191,0,0,0-42.49,4.75,4,4,0,0,0-2,6.59L186,156.07a4,4,0,0,0,6-.14L244.35,92.8A15.65,15.65,0,0,0,247.89,80.91Z" }, null, -1), B = [
  C
], N = { key: 3 }, W = /* @__PURE__ */ o("path", { d: "M138,204a10,10,0,1,1-10-10A10,10,0,0,1,138,204ZM52.44,36A6,6,0,0,0,43.56,44L61.33,63.58a169.41,169.41,0,0,0-41.14,25,6,6,0,1,0,7.62,9.27A157.58,157.58,0,0,1,69.91,73l26.48,29.13a122.21,122.21,0,0,0-44.12,22.19,6,6,0,0,0,7.46,9.41,110,110,0,0,1,45.87-21.47l31.13,34.25A74.4,74.4,0,0,0,128,146a73.44,73.44,0,0,0-43.53,14.15A6,6,0,0,0,88,171a5.93,5.93,0,0,0,3.53-1.15,62,62,0,0,1,59.76-7.31L203.56,220a6,6,0,0,0,8.88-8.08ZM235.81,88.55A170.32,170.32,0,0,0,128,50a173.45,173.45,0,0,0-21.76,1.38,6,6,0,1,0,1.52,11.9A160.58,160.58,0,0,1,128,62,158.26,158.26,0,0,1,228.19,97.82a6,6,0,1,0,7.62-9.27Zm-39.54,45.2A6,6,0,0,0,200,135a6,6,0,0,0,3.73-10.7,122.26,122.26,0,0,0-50.9-23.81,6,6,0,1,0-2.43,11.75A110,110,0,0,1,196.27,133.75Z" }, null, -1), b = [
  W
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M213.92,210.62a8,8,0,1,1-11.84,10.76l-52-57.15a60,60,0,0,0-57.41,7.24,8,8,0,1,1-9.42-12.93A75.43,75.43,0,0,1,128,144c1.28,0,2.55,0,3.82.1L104.9,114.49A108,108,0,0,0,61,135.31,8,8,0,0,1,49.73,134,8,8,0,0,1,51,122.77a124.27,124.27,0,0,1,41.71-21.66L69.37,75.4a155.43,155.43,0,0,0-40.29,24A8,8,0,0,1,18.92,87,171.87,171.87,0,0,1,58,62.86L42.08,45.38A8,8,0,1,1,53.92,34.62ZM128,192a12,12,0,1,0,12,12A12,12,0,0,0,128,192ZM237.08,87A172.3,172.3,0,0,0,106,49.4a8,8,0,1,0,2,15.87A158.33,158.33,0,0,1,128,64a156.25,156.25,0,0,1,98.92,35.37A8,8,0,0,0,237.08,87ZM195,135.31a8,8,0,0,0,11.24-1.3,8,8,0,0,0-1.3-11.24,124.25,124.25,0,0,0-51.73-24.2A8,8,0,1,0,150,114.24,108.12,108.12,0,0,1,195,135.31Z" }, null, -1), V = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M136,204a8,8,0,1,1-8-8A8,8,0,0,1,136,204ZM51,37.31A4,4,0,0,0,45,42.69L64.72,64.34A167.64,167.64,0,0,0,21.46,90.09a4,4,0,1,0,5.08,6.19A159.4,159.4,0,0,1,70.47,70.66l29.64,32.61a120.12,120.12,0,0,0-46.6,22.64,4,4,0,0,0,5,6.27,112,112,0,0,1,47.85-22.07L142,149.37A72.6,72.6,0,0,0,128,148a71.49,71.49,0,0,0-42.36,13.77A4,4,0,0,0,88,169a4.07,4.07,0,0,0,2.36-.76,64,64,0,0,1,62.11-7.38L205,218.69a4,4,0,1,0,5.92-5.38ZM234.54,90.09A168.33,168.33,0,0,0,128,52a171.08,171.08,0,0,0-21.5,1.36,4,4,0,0,0,1,7.94A162.68,162.68,0,0,1,128,60,160.22,160.22,0,0,1,229.46,96.28a4,4,0,0,0,5.08-6.19Zm-37,42.09a4,4,0,1,0,5-6.27,120.17,120.17,0,0,0-50.06-23.42,4,4,0,1,0-1.62,7.83A112.18,112.18,0,0,1,197.52,132.18Z" }, null, -1), q = [
  j
], D = {
  name: "PhWifiSlash"
}, H = /* @__PURE__ */ m({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const s = c, d = l("weight", "regular"), h = l("size", "1em"), A = l("color", "currentColor"), _ = l("mirrored", !1), a = n(() => s.weight ?? d), i = n(() => s.size ?? h), u = n(() => s.color ?? A), p = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : _ ? "scale(-1, 1)" : void 0);
    return (r, F) => (t(), e("svg", g({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      Z(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", y, f)) : a.value === "duotone" ? (t(), e("g", w, x)) : a.value === "fill" ? (t(), e("g", z, B)) : a.value === "light" ? (t(), e("g", N, b)) : a.value === "regular" ? (t(), e("g", E, V)) : a.value === "thin" ? (t(), e("g", $, q)) : M("", !0)
    ], 16, v));
  }
});
export {
  H as default
};
