import { defineComponent as m, inject as a, computed as n, openBlock as t, createElementBlock as e, mergeProps as v, renderSlot as y, createCommentVNode as f, createElementVNode as o } from "vue";
const w = ["width", "height", "fill", "transform"], L = { key: 0 }, M = /* @__PURE__ */ o("path", { d: "M241.73,135l-52,72a12,12,0,0,1-19.46,0L76,76.5,33.73,135A12,12,0,1,1,14.27,121l52-72a12,12,0,0,1,19.46,0L180,179.5,222.27,121A12,12,0,1,1,241.73,135Z" }, null, -1), Z = [
  M
], k = { key: 1 }, x = /* @__PURE__ */ o("path", {
  d: "M76,56l52,72H24Zm156,72H128l52,72Z",
  opacity: "0.2"
}, null, -1), S = /* @__PURE__ */ o("path", { d: "M238.48,132.68l-52,72a8,8,0,0,1-13,0L76,69.66l-45.51,63a8,8,0,1,1-13-9.36l52-72a8,8,0,0,1,13,0l97.51,135,45.51-63a8,8,0,1,1,13,9.36Z" }, null, -1), z = [
  x,
  S
], A = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm-9.85,93.12-40,48A8,8,0,0,1,160,184h-.43a8,8,0,0,1-6.23-3.55l-58-87.09L62.15,133.12a8,8,0,0,1-12.3-10.24l40-48a8,8,0,0,1,12.81.68l58.05,87.09,33.14-39.77a8,8,0,1,1,12.3,10.24Z" }, null, -1), B = [
  C
], H = { key: 3 }, V = /* @__PURE__ */ o("path", { d: "M236.86,131.51l-52,72a6,6,0,0,1-9.72,0L76,66.25,28.86,131.51a6,6,0,1,1-9.72-7l52-72a6,6,0,0,1,9.72,0L180,189.75l47.14-65.26a6,6,0,0,1,9.72,7Z" }, null, -1), N = [
  V
], W = { key: 4 }, b = /* @__PURE__ */ o("path", { d: "M238.48,132.68l-52,72a8,8,0,0,1-13,0L76,69.66l-45.51,63a8,8,0,1,1-13-9.36l52-72a8,8,0,0,1,13,0l97.51,135,45.51-63a8,8,0,1,1,13,9.36Z" }, null, -1), E = [
  b
], P = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M235.24,130.34l-52,72a4,4,0,0,1-6.48,0L76,62.83,27.24,130.34a4,4,0,1,1-6.48-4.68l52-72a4,4,0,0,1,6.48,0L180,193.17l48.76-67.51a4,4,0,0,1,6.48,4.68Z" }, null, -1), j = [
  $
], T = {
  name: "PhWaveTriangle"
}, F = /* @__PURE__ */ m({
  ...T,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const s = d, c = a("weight", "regular"), _ = a("size", "1em"), h = a("color", "currentColor"), u = a("mirrored", !1), l = n(() => s.weight ?? c), i = n(() => s.size ?? _), p = n(() => s.color ?? h), g = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, q) => (t(), e("svg", v({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: p.value,
      transform: g.value
    }, r.$attrs), [
      y(r.$slots, "default"),
      l.value === "bold" ? (t(), e("g", L, Z)) : l.value === "duotone" ? (t(), e("g", k, z)) : l.value === "fill" ? (t(), e("g", A, B)) : l.value === "light" ? (t(), e("g", H, N)) : l.value === "regular" ? (t(), e("g", W, E)) : l.value === "thin" ? (t(), e("g", P, j)) : f("", !0)
    ], 16, w));
  }
});
export {
  F as default
};
