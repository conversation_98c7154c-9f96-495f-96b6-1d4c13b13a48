import { defineComponent as g, inject as a, computed as i, openBlock as t, createElementBlock as e, mergeProps as Z, renderSlot as v, createCommentVNode as A, createElementVNode as o } from "vue";
const y = ["width", "height", "fill", "transform"], f = { key: 0 }, w = /* @__PURE__ */ o("path", { d: "M128,20A108,108,0,1,0,236,128,108.12,108.12,0,0,0,128,20Zm0,192a84,84,0,1,1,84-84A84.09,84.09,0,0,1,128,212Zm-12-80V80a12,12,0,0,1,24,0v52a12,12,0,0,1-24,0Zm28,40a16,16,0,1,1-16-16A16,16,0,0,1,144,172Z" }, null, -1), M = [
  w
], k = { key: 1 }, V = /* @__PURE__ */ o("path", {
  d: "M224,128a96,96,0,1,1-96-96A96,96,0,0,1,224,128Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm-8-80V80a8,8,0,0,1,16,0v56a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,172Z" }, null, -1), C = [
  V,
  x
], S = { key: 2 }, z = /* @__PURE__ */ o("path", { d: "M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm-8,56a8,8,0,0,1,16,0v56a8,8,0,0,1-16,0Zm8,104a12,12,0,1,1,12-12A12,12,0,0,1,128,184Z" }, null, -1), B = [
  z
], N = { key: 3 }, W = /* @__PURE__ */ o("path", { d: "M128,26A102,102,0,1,0,230,128,102.12,102.12,0,0,0,128,26Zm0,192a90,90,0,1,1,90-90A90.1,90.1,0,0,1,128,218Zm-6-82V80a6,6,0,0,1,12,0v56a6,6,0,0,1-12,0Zm16,36a10,10,0,1,1-10-10A10,10,0,0,1,138,172Z" }, null, -1), b = [
  W
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm-8-80V80a8,8,0,0,1,16,0v56a8,8,0,0,1-16,0Zm20,36a12,12,0,1,1-12-12A12,12,0,0,1,140,172Z" }, null, -1), $ = [
  P
], j = { key: 5 }, q = /* @__PURE__ */ o("path", { d: "M128,28A100,100,0,1,0,228,128,100.11,100.11,0,0,0,128,28Zm0,192a92,92,0,1,1,92-92A92.1,92.1,0,0,1,128,220Zm-4-84V80a4,4,0,0,1,8,0v56a4,4,0,0,1-8,0Zm12,36a8,8,0,1,1-8-8A8,8,0,0,1,136,172Z" }, null, -1), D = [
  q
], F = {
  name: "PhWarningCircle"
}, I = /* @__PURE__ */ g({
  ...F,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const n = c, d = a("weight", "regular"), _ = a("size", "1em"), h = a("color", "currentColor"), m = a("mirrored", !1), s = i(() => n.weight ?? d), l = i(() => n.size ?? _), u = i(() => n.color ?? h), p = i(() => n.mirrored !== void 0 ? n.mirrored ? "scale(-1, 1)" : void 0 : m ? "scale(-1, 1)" : void 0);
    return (r, G) => (t(), e("svg", Z({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      v(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", f, M)) : s.value === "duotone" ? (t(), e("g", k, C)) : s.value === "fill" ? (t(), e("g", S, B)) : s.value === "light" ? (t(), e("g", N, b)) : s.value === "regular" ? (t(), e("g", E, $)) : s.value === "thin" ? (t(), e("g", j, D)) : A("", !0)
    ], 16, y));
  }
});
export {
  I as default
};
