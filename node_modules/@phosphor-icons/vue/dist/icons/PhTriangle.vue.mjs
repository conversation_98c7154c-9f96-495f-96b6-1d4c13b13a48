import { defineComponent as m, inject as n, computed as l, openBlock as t, createElementBlock as e, mergeProps as A, renderSlot as v, createCommentVNode as y, createElementVNode as o } from "vue";
const L = ["width", "height", "fill", "transform"], M = { key: 0 }, f = /* @__PURE__ */ o("path", { d: "M240.26,186.1,152.81,34.23h0a28.74,28.74,0,0,0-49.62,0L15.74,186.1a27.45,27.45,0,0,0,0,27.71A28.31,28.31,0,0,0,40.55,228h174.9a28.31,28.31,0,0,0,24.79-14.19A27.45,27.45,0,0,0,240.26,186.1Zm-20.8,15.7a4.46,4.46,0,0,1-4,2.2H40.55a4.46,4.46,0,0,1-4-2.2,3.56,3.56,0,0,1,0-3.73L124,46.2a4.75,4.75,0,0,1,8,0l87.45,151.87A3.56,3.56,0,0,1,219.46,201.8Z" }, null, -1), Z = [
  f
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M215.46,216H40.54C27.92,216,20,202.79,26.13,192.09L113.59,40.22c6.3-11,22.52-11,28.82,0l87.46,151.87C236,202.79,228.08,216,215.46,216Z",
  opacity: "0.2"
}, null, -1), H = /* @__PURE__ */ o("path", { d: "M236.8,188.09,149.35,36.22a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.34,24.34,0,0,0,40.55,224h174.9a24.34,24.34,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8Z" }, null, -1), C = [
  k,
  H
], x = { key: 2 }, S = /* @__PURE__ */ o("path", { d: "M236.78,211.81A24.34,24.34,0,0,1,215.45,224H40.55a24.34,24.34,0,0,1-21.33-12.19,23.51,23.51,0,0,1,0-23.72L106.65,36.22a24.76,24.76,0,0,1,42.7,0L236.8,188.09A23.51,23.51,0,0,1,236.78,211.81Z" }, null, -1), z = [
  S
], B = { key: 3 }, N = /* @__PURE__ */ o("path", { d: "M235.07,189.09,147.61,37.22a22.75,22.75,0,0,0-39.22,0L20.93,189.09a21.53,21.53,0,0,0,0,21.72A22.35,22.35,0,0,0,40.55,222h174.9a22.35,22.35,0,0,0,19.6-11.19A21.53,21.53,0,0,0,235.07,189.09ZM224.66,204.8a10.46,10.46,0,0,1-9.21,5.2H40.55a10.46,10.46,0,0,1-9.21-5.2,9.49,9.49,0,0,1,0-9.72L118.79,43.21a10.75,10.75,0,0,1,18.42,0l87.46,151.87A9.49,9.49,0,0,1,224.66,204.8Z" }, null, -1), b = [
  N
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M236.8,188.09,149.35,36.22a24.76,24.76,0,0,0-42.7,0L19.2,188.09a23.51,23.51,0,0,0,0,23.72A24.34,24.34,0,0,0,40.55,224h174.9a24.34,24.34,0,0,0,21.33-12.19A23.51,23.51,0,0,0,236.8,188.09ZM222.93,203.8a8.5,8.5,0,0,1-7.48,4.2H40.55a8.5,8.5,0,0,1-7.48-4.2,7.59,7.59,0,0,1,0-7.72L120.52,44.21a8.75,8.75,0,0,1,15,0l87.45,151.87A7.59,7.59,0,0,1,222.93,203.8Z" }, null, -1), V = [
  P
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M233.34,190.09,145.88,38.22a20.75,20.75,0,0,0-35.76,0L22.66,190.09a19.52,19.52,0,0,0,0,19.71A20.36,20.36,0,0,0,40.54,220H215.46a20.36,20.36,0,0,0,17.86-10.2A19.52,19.52,0,0,0,233.34,190.09ZM226.4,205.8a12.47,12.47,0,0,1-10.94,6.2H40.54a12.47,12.47,0,0,1-10.94-6.2,11.45,11.45,0,0,1,0-11.72L117.05,42.21a12.76,12.76,0,0,1,21.9,0L226.4,194.08A11.45,11.45,0,0,1,226.4,205.8Z" }, null, -1), j = [
  $
], T = {
  name: "PhTriangle"
}, F = /* @__PURE__ */ m({
  ...T,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const a = c, d = n("weight", "regular"), h = n("size", "1em"), _ = n("color", "currentColor"), u = n("mirrored", !1), s = l(() => a.weight ?? d), i = l(() => a.size ?? h), p = l(() => a.color ?? _), g = l(() => a.mirrored !== void 0 ? a.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, q) => (t(), e("svg", A({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: p.value,
      transform: g.value
    }, r.$attrs), [
      v(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", M, Z)) : s.value === "duotone" ? (t(), e("g", w, C)) : s.value === "fill" ? (t(), e("g", x, z)) : s.value === "light" ? (t(), e("g", B, b)) : s.value === "regular" ? (t(), e("g", E, V)) : s.value === "thin" ? (t(), e("g", W, j)) : y("", !0)
    ], 16, L));
  }
});
export {
  F as default
};
