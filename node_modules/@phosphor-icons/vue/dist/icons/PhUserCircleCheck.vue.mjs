import { defineComponent as M, inject as l, computed as n, openBlock as t, createElementBlock as e, mergeProps as Z, renderSlot as g, createCommentVNode as m, createElementVNode as o } from "vue";
const v = ["width", "height", "fill", "transform"], y = { key: 0 }, f = /* @__PURE__ */ o("path", { d: "M220.69,100.17A12,12,0,0,0,210.84,114,85,85,0,0,1,212,128,83.57,83.57,0,0,1,194,179.94a83.48,83.48,0,0,0-29-23.42,52,52,0,1,0-74,0,83.48,83.48,0,0,0-29,23.42A83.94,83.94,0,0,1,128,44a85,85,0,0,1,14,1.16,12,12,0,0,0,4-23.67A108.1,108.1,0,0,0,20,128a108,108,0,0,0,216,0,109.19,109.19,0,0,0-1.49-18A12,12,0,0,0,220.69,100.17ZM100,120a28,28,0,1,1,28,28A28,28,0,0,1,100,120ZM79.57,196.57a60,60,0,0,1,96.86,0,83.72,83.72,0,0,1-96.86,0ZM240.49,48.49l-32,32a12,12,0,0,1-17,0l-16-16a12,12,0,0,1,17-17L200,55l23.51-23.52a12,12,0,1,1,17,17Z" }, null, -1), w = [
  f
], k = { key: 1 }, C = /* @__PURE__ */ o("path", {
  d: "M224,128a95.76,95.76,0,0,1-31.8,71.37A72,72,0,0,0,128,160a40,40,0,1,0-40-40,40,40,0,0,0,40,40,72,72,0,0,0-64.2,39.37h0A96,96,0,1,1,224,128Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M221.35,104.11a8,8,0,0,0-6.57,9.21A88.85,88.85,0,0,1,216,128a87.62,87.62,0,0,1-22.24,58.41,79.66,79.66,0,0,0-36.06-28.75,48,48,0,1,0-59.4,0,79.66,79.66,0,0,0-36.06,28.75A88,88,0,0,1,128,40a88.76,88.76,0,0,1,14.68,1.22,8,8,0,0,0,2.64-15.78,103.92,103.92,0,1,0,85.24,85.24A8,8,0,0,0,221.35,104.11ZM96,120a32,32,0,1,1,32,32A32,32,0,0,1,96,120ZM74.08,197.5a64,64,0,0,1,107.84,0,87.83,87.83,0,0,1-107.84,0ZM237.66,45.66l-32,32a8,8,0,0,1-11.32,0l-16-16a8,8,0,0,1,11.32-11.32L200,60.69l26.34-26.35a8,8,0,0,1,11.32,11.32Z" }, null, -1), L = [
  C,
  x
], S = { key: 2 }, z = /* @__PURE__ */ o("path", { d: "M230.56,110.68a103.92,103.92,0,1,1-85.24-85.24,8,8,0,0,1-2.64,15.78A88.07,88.07,0,0,0,40,128a87.62,87.62,0,0,0,22.24,58.41A79.71,79.71,0,0,1,84,165.1a4,4,0,0,1,4.84.32,59.8,59.8,0,0,0,78.26,0,4,4,0,0,1,4.84-.32,79.86,79.86,0,0,1,21.79,21.31A87.62,87.62,0,0,0,216,128a88.85,88.85,0,0,0-1.22-14.68,8,8,0,1,1,15.78-2.64ZM84,120a44,44,0,1,0,44-44A44,44,0,0,0,84,120ZM237.66,34.34a8,8,0,0,0-11.32,0L200,60.69,189.66,50.34a8,8,0,0,0-11.32,11.32l16,16a8,8,0,0,0,11.32,0l32-32A8,8,0,0,0,237.66,34.34Z" }, null, -1), B = [
  z
], N = { key: 3 }, b = /* @__PURE__ */ o("path", { d: "M221.68,106.08a6,6,0,0,0-4.92,6.91A91.66,91.66,0,0,1,218,128a89.65,89.65,0,0,1-24.49,61.64,77.53,77.53,0,0,0-40-31.38,46,46,0,1,0-51,0,77.53,77.53,0,0,0-40,31.38A89.95,89.95,0,0,1,128,38a91.57,91.57,0,0,1,15,1.24,6,6,0,1,0,2-11.83,101.9,101.9,0,1,0,83.6,83.6A6,6,0,0,0,221.68,106.08ZM94,120a34,34,0,1,1,34,34A34,34,0,0,1,94,120ZM71.44,198a66,66,0,0,1,113.12,0,89.8,89.8,0,0,1-113.12,0ZM236.24,44.24l-32,32a6,6,0,0,1-8.48,0l-16-16a6,6,0,0,1,8.48-8.48L200,63.51l27.76-27.75a6,6,0,0,1,8.48,8.48Z" }, null, -1), E = [
  b
], P = { key: 4 }, V = /* @__PURE__ */ o("path", { d: "M221.35,104.11a8,8,0,0,0-6.57,9.21A88.85,88.85,0,0,1,216,128a87.62,87.62,0,0,1-22.24,58.41,79.66,79.66,0,0,0-36.06-28.75,48,48,0,1,0-59.4,0,79.66,79.66,0,0,0-36.06,28.75A88,88,0,0,1,128,40a88.76,88.76,0,0,1,14.68,1.22,8,8,0,0,0,2.64-15.78,103.92,103.92,0,1,0,85.24,85.24A8,8,0,0,0,221.35,104.11ZM96,120a32,32,0,1,1,32,32A32,32,0,0,1,96,120ZM74.08,197.5a64,64,0,0,1,107.84,0,87.83,87.83,0,0,1-107.84,0ZM237.66,45.66l-32,32a8,8,0,0,1-11.32,0l-16-16a8,8,0,0,1,11.32-11.32L200,60.69l26.34-26.35a8,8,0,0,1,11.32,11.32Z" }, null, -1), W = [
  V
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M222,108.05a4,4,0,0,0-3.28,4.61A93.4,93.4,0,0,1,220,128a91.71,91.71,0,0,1-26.83,64.87,75.61,75.61,0,0,0-44.51-34,44,44,0,1,0-41.32,0,75.61,75.61,0,0,0-44.51,34A92,92,0,0,1,128,36a93.4,93.4,0,0,1,15.34,1.27,4,4,0,0,0,1.32-7.89A100,100,0,1,0,228,128a101.78,101.78,0,0,0-1.38-16.66A4,4,0,0,0,222,108.05ZM92,120a36,36,0,1,1,36,36A36,36,0,0,1,92,120ZM68.87,198.42a68,68,0,0,1,118.26,0,91.8,91.8,0,0,1-118.26,0Zm166-155.59-32,32a4,4,0,0,1-5.66,0l-16-16a4,4,0,0,1,5.66-5.66L200,66.34l29.17-29.17a4,4,0,1,1,5.66,5.66Z" }, null, -1), U = [
  j
], q = {
  name: "PhUserCircleCheck"
}, G = /* @__PURE__ */ M({
  ...q,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const s = c, d = l("weight", "regular"), _ = l("size", "1em"), h = l("color", "currentColor"), u = l("mirrored", !1), a = n(() => s.weight ?? d), i = n(() => s.size ?? _), A = n(() => s.color ?? h), p = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, D) => (t(), e("svg", Z({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: A.value,
      transform: p.value
    }, r.$attrs), [
      g(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", y, w)) : a.value === "duotone" ? (t(), e("g", k, L)) : a.value === "fill" ? (t(), e("g", S, B)) : a.value === "light" ? (t(), e("g", N, E)) : a.value === "regular" ? (t(), e("g", P, W)) : a.value === "thin" ? (t(), e("g", $, U)) : m("", !0)
    ], 16, v));
  }
});
export {
  G as default
};
