import { defineComponent as v, inject as l, computed as n, openBlock as t, createElementBlock as e, mergeProps as m, renderSlot as A, createCommentVNode as g, createElementVNode as o } from "vue";
const V = ["width", "height", "fill", "transform"], Z = { key: 0 }, y = /* @__PURE__ */ o("path", { d: "M243.78,156.53l-12-96A28,28,0,0,0,204,36H32A20,20,0,0,0,12,56v88a20,20,0,0,0,20,20H72.58l36.69,73.37A12,12,0,0,0,120,244a44.05,44.05,0,0,0,44-44V188h52a28,28,0,0,0,27.78-31.47ZM68,140H36V60H68Zm151,22.65a4,4,0,0,1-3,1.35H152a12,12,0,0,0-12,12v24a20,20,0,0,1-13.18,18.8L92,149.17V60H204a4,4,0,0,1,4,3.5l12,96A4,4,0,0,1,219,162.65Z" }, null, -1), M = [
  y
], f = { key: 1 }, w = /* @__PURE__ */ o("path", {
  d: "M80,48V152H32a8,8,0,0,1-8-8V56a8,8,0,0,1,8-8Z",
  opacity: "0.2"
}, null, -1), k = /* @__PURE__ */ o("path", { d: "M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z" }, null, -1), x = [
  w,
  k
], S = { key: 2 }, z = /* @__PURE__ */ o("path", { d: "M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Z" }, null, -1), C = [
  z
], L = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M237.83,157.27l-12-96A22,22,0,0,0,204,42H32A14,14,0,0,0,18,56v88a14,14,0,0,0,14,14H76.29l38.34,76.68A6,6,0,0,0,120,238a38,38,0,0,0,38-38V182h58a22,22,0,0,0,21.83-24.73ZM74,146H32a2,2,0,0,1-2-2V56a2,2,0,0,1,2-2H74Zm149.5,20.62A9.89,9.89,0,0,1,216,170H152a6,6,0,0,0-6,6v24a26,26,0,0,1-22.42,25.75L86,150.58V54H204a10,10,0,0,1,9.92,8.76l12,96A9.89,9.89,0,0,1,223.5,166.62Z" }, null, -1), b = [
  B
], N = { key: 4 }, E = /* @__PURE__ */ o("path", { d: "M239.82,157l-12-96A24,24,0,0,0,204,40H32A16,16,0,0,0,16,56v88a16,16,0,0,0,16,16H75.06l37.78,75.58A8,8,0,0,0,120,240a40,40,0,0,0,40-40V184h56a24,24,0,0,0,23.82-27ZM72,144H32V56H72Zm150,21.29a7.88,7.88,0,0,1-6,2.71H152a8,8,0,0,0-8,8v24a24,24,0,0,1-19.29,23.54L88,150.11V56H204a8,8,0,0,1,7.94,7l12,96A7.87,7.87,0,0,1,222,165.29Z" }, null, -1), P = [
  E
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M235.85,157.52l-12-96A20,20,0,0,0,204,44H32A12,12,0,0,0,20,56v88a12,12,0,0,0,12,12H77.53l38.89,77.79A4,4,0,0,0,120,236a36,36,0,0,0,36-36V180h60a20,20,0,0,0,19.85-22.48ZM76,148H32a4,4,0,0,1-4-4V56a4,4,0,0,1,4-4H76Zm149,19.94a12,12,0,0,1-9,4.06H152a4,4,0,0,0-4,4v24a28,28,0,0,1-25.58,27.9L84,151.06V52H204a12,12,0,0,1,11.91,10.51l12,96A12,12,0,0,1,225,167.94Z" }, null, -1), j = [
  $
], D = {
  name: "PhThumbsDown"
}, F = /* @__PURE__ */ v({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const s = d, c = l("weight", "regular"), h = l("size", "1em"), _ = l("color", "currentColor"), H = l("mirrored", !1), a = n(() => s.weight ?? c), i = n(() => s.size ?? h), u = n(() => s.color ?? _), p = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : H ? "scale(-1, 1)" : void 0);
    return (r, T) => (t(), e("svg", m({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      A(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", Z, M)) : a.value === "duotone" ? (t(), e("g", f, x)) : a.value === "fill" ? (t(), e("g", S, C)) : a.value === "light" ? (t(), e("g", L, b)) : a.value === "regular" ? (t(), e("g", N, P)) : a.value === "thin" ? (t(), e("g", W, j)) : g("", !0)
    ], 16, V));
  }
});
export {
  F as default
};
