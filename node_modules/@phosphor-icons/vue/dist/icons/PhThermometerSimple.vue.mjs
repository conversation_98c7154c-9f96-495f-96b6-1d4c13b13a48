import { defineComponent as p, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as g, renderSlot as Z, createCommentVNode as V, createElementVNode as o } from "vue";
const A = ["width", "height", "fill", "transform"], y = { key: 0 }, M = /* @__PURE__ */ o("path", { d: "M180,150.69V56A52,52,0,0,0,76,56v94.69a64,64,0,1,0,104,0ZM128,228a40,40,0,0,1-30.91-65.39,12,12,0,0,0,2.91-7.83V56a28,28,0,0,1,56,0v98.77a12,12,0,0,0,2.77,7.68A40,40,0,0,1,128,228Zm24-40a24,24,0,1,1-36-20.78V92a12,12,0,0,1,24,0v75.22A24,24,0,0,1,152,188Z" }, null, -1), f = [
  M
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M160,138V48a32,32,0,0,0-64,0v90a56,56,0,1,0,64,0Zm-32,70a24,24,0,1,1,24-24A24,24,0,0,1,128,208Z",
  opacity: "0.2"
}, null, -1), S = /* @__PURE__ */ o("path", { d: "M136,153V88a8,8,0,0,0-16,0v65a32,32,0,1,0,16,0Zm-8,47a16,16,0,1,1,16-16A16,16,0,0,1,128,200Zm40-66V48a40,40,0,0,0-80,0v86a64,64,0,1,0,80,0Zm-40,98a48,48,0,0,1-27.42-87.4A8,8,0,0,0,104,138V48a24,24,0,0,1,48,0v90a8,8,0,0,0,3.42,6.56A48,48,0,0,1,128,232Z" }, null, -1), x = [
  k,
  S
], z = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M160,146.08V40a32,32,0,0,0-64,0V146.08a56,56,0,1,0,64,0ZM128,24a16,16,0,0,1,16,16v64H112V40A16,16,0,0,1,128,24Z" }, null, -1), B = [
  C
], N = { key: 3 }, b = /* @__PURE__ */ o("path", { d: "M134,154.6V88a6,6,0,0,0-12,0v66.6a30,30,0,1,0,12,0ZM128,202a18,18,0,1,1,18-18A18,18,0,0,1,128,202Zm38-67V48a38,38,0,0,0-76,0v87a62,62,0,1,0,76,0Zm-38,99a50,50,0,0,1-28.57-91A6,6,0,0,0,102,138V48a26,26,0,0,1,52,0v90a6,6,0,0,0,2.57,4.92A50,50,0,0,1,128,234Z" }, null, -1), E = [
  b
], P = { key: 4 }, W = /* @__PURE__ */ o("path", { d: "M136,153V88a8,8,0,0,0-16,0v65a32,32,0,1,0,16,0Zm-8,47a16,16,0,1,1,16-16A16,16,0,0,1,128,200Zm40-66V48a40,40,0,0,0-80,0v86a64,64,0,1,0,80,0Zm-40,98a48,48,0,0,1-27.42-87.4A8,8,0,0,0,104,138V48a24,24,0,0,1,48,0v90a8,8,0,0,0,3.42,6.56A48,48,0,0,1,128,232Z" }, null, -1), $ = [
  W
], j = { key: 5 }, H = /* @__PURE__ */ o("path", { d: "M132,156.29V88a4,4,0,0,0-8,0v68.29a28,28,0,1,0,8,0ZM128,204a20,20,0,1,1,20-20A20,20,0,0,1,128,204Zm36-68V48a36,36,0,0,0-72,0v88a60,60,0,1,0,72,0ZM128,236a52,52,0,0,1-29.71-94.68A4,4,0,0,0,100,138V48a28,28,0,0,1,56,0v90a4,4,0,0,0,1.71,3.28A52,52,0,0,1,128,236Z" }, null, -1), T = [
  H
], q = {
  name: "PhThermometerSimple"
}, G = /* @__PURE__ */ p({
  ...q,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const s = d, c = n("weight", "regular"), _ = n("size", "1em"), h = n("color", "currentColor"), m = n("mirrored", !1), a = i(() => s.weight ?? c), l = i(() => s.size ?? _), v = i(() => s.color ?? h), u = i(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : m ? "scale(-1, 1)" : void 0);
    return (r, D) => (t(), e("svg", g({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: v.value,
      transform: u.value
    }, r.$attrs), [
      Z(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", y, f)) : a.value === "duotone" ? (t(), e("g", w, x)) : a.value === "fill" ? (t(), e("g", z, B)) : a.value === "light" ? (t(), e("g", N, E)) : a.value === "regular" ? (t(), e("g", P, $)) : a.value === "thin" ? (t(), e("g", j, T)) : V("", !0)
    ], 16, A));
  }
});
export {
  G as default
};
