import { defineComponent as g, inject as a, computed as i, openBlock as t, createElementBlock as e, mergeProps as Z, renderSlot as v, createCommentVNode as A, createElementVNode as o } from "vue";
const y = ["width", "height", "fill", "transform"], f = { key: 0 }, M = /* @__PURE__ */ o("path", { d: "M144,204a16,16,0,1,1-16-16A16,16,0,0,1,144,204Zm63.45-84.36a128,128,0,0,0-158.9,0,12,12,0,0,0,14.9,18.81,104,104,0,0,1,129.1,0,12,12,0,0,0,14.9-18.81ZM175.07,155.3a80.05,80.05,0,0,0-94.14,0,12,12,0,0,0,14.14,19.4,56,56,0,0,1,65.86,0,12,12,0,1,0,14.14-19.4Z" }, null, -1), w = [
  M
], k = { key: 1 }, x = /* @__PURE__ */ o("path", {
  d: "M202.33,130.94l-68.18,82.18a8,8,0,0,1-12.3,0L53.67,130.94a116,116,0,0,1,148.66,0Z",
  opacity: "0.2"
}, null, -1), S = /* @__PURE__ */ o("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91ZM128,208l-62.87-75.8a107.89,107.89,0,0,1,125.74,0Zm73.12-88.16a124,124,0,0,0-146.24,0L24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), z = [
  x,
  S
], C = { key: 2 }, B = /* @__PURE__ */ o("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91Zm-46.77,38.94a124,124,0,0,0-146.24,0L24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), L = [
  B
], N = { key: 3 }, W = /* @__PURE__ */ o("path", { d: "M138,204a10,10,0,1,1-10-10A10,10,0,0,1,138,204Zm65.73-79.66a122,122,0,0,0-151.46,0,6,6,0,0,0,7.46,9.41,110,110,0,0,1,136.54,0A6,6,0,0,0,200,135a6,6,0,0,0,3.73-10.7Zm-32.2,35.81a74,74,0,0,0-87.06,0,6,6,0,0,0,7.06,9.7,62,62,0,0,1,72.94,0,6,6,0,0,0,8.38-1.32A6,6,0,0,0,171.53,160.15Z" }, null, -1), b = [
  W
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M140,204a12,12,0,1,1-12-12A12,12,0,0,1,140,204Zm65-81.23a124,124,0,0,0-153.94,0A8,8,0,0,0,61,135.31a108,108,0,0,1,134.06,0,8,8,0,0,0,11.24-1.3A8,8,0,0,0,205,122.77Zm-32.26,35.76a76.05,76.05,0,0,0-89.42,0,8,8,0,0,0,9.42,12.94,60,60,0,0,1,70.58,0,8,8,0,1,0,9.42-12.94Z" }, null, -1), V = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M136,204a8,8,0,1,1-8-8A8,8,0,0,1,136,204Zm66.48-78.09a120,120,0,0,0-149,0,4,4,0,0,0,5,6.27,112,112,0,0,1,139,0,4,4,0,0,0,5-6.27Zm-32.13,35.86a72,72,0,0,0-84.7,0,4,4,0,1,0,4.71,6.46,64,64,0,0,1,75.28,0,4,4,0,1,0,4.71-6.46Z" }, null, -1), q = [
  j
], D = {
  name: "PhWifiMedium"
}, H = /* @__PURE__ */ g({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const n = d, c = a("weight", "regular"), h = a("size", "1em"), _ = a("color", "currentColor"), u = a("mirrored", !1), s = i(() => n.weight ?? c), l = i(() => n.size ?? h), m = i(() => n.color ?? _), p = i(() => n.mirrored !== void 0 ? n.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, F) => (t(), e("svg", Z({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: m.value,
      transform: p.value
    }, r.$attrs), [
      v(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", f, w)) : s.value === "duotone" ? (t(), e("g", k, z)) : s.value === "fill" ? (t(), e("g", C, L)) : s.value === "light" ? (t(), e("g", N, b)) : s.value === "regular" ? (t(), e("g", E, V)) : s.value === "thin" ? (t(), e("g", $, q)) : A("", !0)
    ], 16, y));
  }
});
export {
  H as default
};
