import { defineComponent as p, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as g, renderSlot as H, createCommentVNode as v, createElementVNode as o } from "vue";
const M = ["width", "height", "fill", "transform"], y = { key: 0 }, A = /* @__PURE__ */ o("path", { d: "M152,80a12,12,0,0,1,12-12h80a12,12,0,0,1,0,24H164A12,12,0,0,1,152,80Zm92,36H164a12,12,0,0,0,0,24h80a12,12,0,0,0,0-24Zm0,48H188a12,12,0,0,0,0,24h56a12,12,0,0,0,0-24Zm-88.38,25a12,12,0,1,1-23.24,6c-5.72-22.23-28.24-39-52.38-39s-46.66,16.76-52.38,39a12,12,0,1,1-23.24-6c5.38-20.9,20.09-38.16,39.11-48a52,52,0,1,1,73,0C135.53,150.85,150.24,168.11,155.62,189ZM80,132a28,28,0,1,0-28-28A28,28,0,0,0,80,132Z" }, null, -1), f = [
  A
], C = { key: 1 }, w = /* @__PURE__ */ o("path", {
  d: "M120,104A40,40,0,1,1,80,64,40,40,0,0,1,120,104Z",
  opacity: "0.2"
}, null, -1), k = /* @__PURE__ */ o("path", { d: "M152,80a8,8,0,0,1,8-8h88a8,8,0,0,1,0,16H160A8,8,0,0,1,152,80Zm96,40H160a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16Zm0,48H184a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16Zm-96.25,22a8,8,0,0,1-5.76,9.74,7.55,7.55,0,0,1-2,.26,8,8,0,0,1-7.75-6c-6.16-23.94-30.34-42-56.25-42s-50.09,18.05-56.25,42a8,8,0,0,1-15.5-4c5.59-21.71,21.84-39.29,42.46-48a48,48,0,1,1,58.58,0C129.91,150.71,146.16,168.29,151.75,190ZM80,136a32,32,0,1,0-32-32A32,32,0,0,0,80,136Z" }, null, -1), x = [
  w,
  k
], S = { key: 2 }, z = /* @__PURE__ */ o("path", { d: "M152,80a8,8,0,0,1,8-8h88a8,8,0,0,1,0,16H160A8,8,0,0,1,152,80Zm96,40H160a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16Zm0,48H184a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16ZM109.29,142a48,48,0,1,0-58.58,0c-20.62,8.73-36.87,26.3-42.46,48A8,8,0,0,0,16,200H144a8,8,0,0,0,7.75-10C146.16,168.29,129.91,150.72,109.29,142Z" }, null, -1), B = [
  z
], N = { key: 3 }, b = /* @__PURE__ */ o("path", { d: "M154,80a6,6,0,0,1,6-6h88a6,6,0,0,1,0,12H160A6,6,0,0,1,154,80Zm94,42H160a6,6,0,0,0,0,12h88a6,6,0,0,0,0-12Zm0,48H184a6,6,0,0,0,0,12h64a6,6,0,0,0,0-12Zm-98.19,20.5a6,6,0,1,1-11.62,3C131.7,168.29,107.23,150,80,150s-51.7,18.29-58.19,43.49a6,6,0,1,1-11.62-3c5.74-22.28,23-40.07,44.67-48a46,46,0,1,1,50.28,0C126.79,150.43,144.08,168.22,149.81,190.5ZM80,138a34,34,0,1,0-34-34A34,34,0,0,0,80,138Z" }, null, -1), E = [
  b
], P = { key: 4 }, V = /* @__PURE__ */ o("path", { d: "M152,80a8,8,0,0,1,8-8h88a8,8,0,0,1,0,16H160A8,8,0,0,1,152,80Zm96,40H160a8,8,0,0,0,0,16h88a8,8,0,0,0,0-16Zm0,48H184a8,8,0,0,0,0,16h64a8,8,0,0,0,0-16Zm-96.25,22a8,8,0,0,1-5.76,9.74,7.55,7.55,0,0,1-2,.26,8,8,0,0,1-7.75-6c-6.16-23.94-30.34-42-56.25-42s-50.09,18.05-56.25,42a8,8,0,0,1-15.5-4c5.59-21.71,21.84-39.29,42.46-48a48,48,0,1,1,58.58,0C129.91,150.71,146.16,168.29,151.75,190ZM80,136a32,32,0,1,0-32-32A32,32,0,0,0,80,136Z" }, null, -1), W = [
  V
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M156,80a4,4,0,0,1,4-4h88a4,4,0,0,1,0,8H160A4,4,0,0,1,156,80Zm92,44H160a4,4,0,0,0,0,8h88a4,4,0,0,0,0-8Zm0,48H184a4,4,0,0,0,0,8h64a4,4,0,0,0,0-8ZM147.87,191a4,4,0,0,1-2.87,4.87,3.87,3.87,0,0,1-1,.13,4,4,0,0,1-3.87-3c-6.71-26.08-32-45-60.13-45s-53.41,18.92-60.13,45a4,4,0,1,1-7.74-2c5.92-23,24.57-41.14,47.52-48a44,44,0,1,1,40.7,0C123.3,149.86,142,168,147.87,191ZM80,140a36,36,0,1,0-36-36A36,36,0,0,0,80,140Z" }, null, -1), L = [
  j
], U = {
  name: "PhUserList"
}, F = /* @__PURE__ */ p({
  ...U,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(l) {
    const s = l, r = n("weight", "regular"), d = n("size", "1em"), _ = n("color", "currentColor"), m = n("mirrored", !1), a = i(() => s.weight ?? r), c = i(() => s.size ?? d), u = i(() => s.color ?? _), Z = i(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : m ? "scale(-1, 1)" : void 0);
    return (h, q) => (t(), e("svg", g({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: c.value,
      height: c.value,
      fill: u.value,
      transform: Z.value
    }, h.$attrs), [
      H(h.$slots, "default"),
      a.value === "bold" ? (t(), e("g", y, f)) : a.value === "duotone" ? (t(), e("g", C, x)) : a.value === "fill" ? (t(), e("g", S, B)) : a.value === "light" ? (t(), e("g", N, E)) : a.value === "regular" ? (t(), e("g", P, W)) : a.value === "thin" ? (t(), e("g", $, L)) : v("", !0)
    ], 16, M));
  }
});
export {
  F as default
};
