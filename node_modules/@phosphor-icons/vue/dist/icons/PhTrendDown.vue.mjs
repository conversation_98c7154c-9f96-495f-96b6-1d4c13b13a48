import { defineComponent as v, inject as a, computed as n, openBlock as t, createElementBlock as e, mergeProps as m, renderSlot as L, createCommentVNode as y, createElementVNode as o } from "vue";
const f = ["width", "height", "fill", "transform"], w = { key: 0 }, M = /* @__PURE__ */ o("path", { d: "M244,128v64a12,12,0,0,1-12,12H168a12,12,0,0,1,0-24h35l-67-67-31.51,31.52a12,12,0,0,1-17,0l-72-72a12,12,0,0,1,17-17L96,119l31.51-31.52a12,12,0,0,1,17,0L220,163V128a12,12,0,0,1,24,0Z" }, null, -1), k = [
  M
], Z = { key: 1 }, H = /* @__PURE__ */ o("path", {
  d: "M232,128v64H168Z",
  opacity: "0.2"
}, null, -1), V = /* @__PURE__ */ o("path", { d: "M235.06,120.61a8,8,0,0,0-8.72,1.73L200,148.69,141.66,90.34a8,8,0,0,0-11.32,0L96,124.69,29.66,58.34A8,8,0,0,0,18.34,69.66l72,72a8,8,0,0,0,11.32,0L136,107.31,188.69,160l-26.35,26.34A8,8,0,0,0,168,200h64a8,8,0,0,0,8-8V128A8,8,0,0,0,235.06,120.61ZM224,184H187.31L224,147.31Z" }, null, -1), x = [
  H,
  V
], A = { key: 2 }, S = /* @__PURE__ */ o("path", { d: "M240,128v64a8,8,0,0,1-8,8H168a8,8,0,0,1-5.66-13.66L188.69,160,136,107.31l-34.34,34.35a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,29.66,58.34L96,124.69l34.34-34.35a8,8,0,0,1,11.32,0L200,148.69l26.34-26.35A8,8,0,0,1,240,128Z" }, null, -1), z = [
  S
], C = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M238,128v64a6,6,0,0,1-6,6H168a6,6,0,0,1,0-12h49.52L136,104.49l-35.76,35.75a6,6,0,0,1-8.48,0l-72-72a6,6,0,0,1,8.48-8.48L96,127.51l35.76-35.75a6,6,0,0,1,8.48,0L226,177.52V128a6,6,0,0,1,12,0Z" }, null, -1), N = [
  B
], b = { key: 4 }, E = /* @__PURE__ */ o("path", { d: "M240,128v64a8,8,0,0,1-8,8H168a8,8,0,0,1,0-16h44.69L136,107.31l-34.34,34.35a8,8,0,0,1-11.32,0l-72-72A8,8,0,0,1,29.66,58.34L96,124.69l34.34-34.35a8,8,0,0,1,11.32,0L224,172.69V128a8,8,0,0,1,16,0Z" }, null, -1), P = [
  E
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M236,128v64a4,4,0,0,1-4,4H168a4,4,0,0,1,0-8h54.34L136,101.66,98.83,138.83a4,4,0,0,1-5.66,0l-72-72a4,4,0,0,1,5.66-5.66L96,130.34l37.17-37.17a4,4,0,0,1,5.66,0L228,182.34V128a4,4,0,0,1,8,0Z" }, null, -1), j = [
  $
], D = {
  name: "PhTrendDown"
}, F = /* @__PURE__ */ v({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const s = d, c = a("weight", "regular"), h = a("size", "1em"), _ = a("color", "currentColor"), u = a("mirrored", !1), l = n(() => s.weight ?? c), i = n(() => s.size ?? h), p = n(() => s.color ?? _), g = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, T) => (t(), e("svg", m({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: p.value,
      transform: g.value
    }, r.$attrs), [
      L(r.$slots, "default"),
      l.value === "bold" ? (t(), e("g", w, k)) : l.value === "duotone" ? (t(), e("g", Z, x)) : l.value === "fill" ? (t(), e("g", A, z)) : l.value === "light" ? (t(), e("g", C, N)) : l.value === "regular" ? (t(), e("g", b, P)) : l.value === "thin" ? (t(), e("g", W, j)) : y("", !0)
    ], 16, f));
  }
});
export {
  F as default
};
