import { defineComponent as g, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as Z, renderSlot as A, createCommentVNode as v, createElementVNode as o } from "vue";
const M = ["width", "height", "fill", "transform"], y = { key: 0 }, f = /* @__PURE__ */ o("path", { d: "M196,68a60,60,0,0,0-48,96H108a60,60,0,1,0-48,24H196a60,60,0,0,0,0-120ZM24,128a36,36,0,1,1,36,36A36,36,0,0,1,24,128Zm172,36a36,36,0,1,1,36-36A36,36,0,0,1,196,164Z" }, null, -1), H = [
  f
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M104,128A48,48,0,1,1,56,80,48,48,0,0,1,104,128Zm96-48a48,48,0,1,0,48,48A48,48,0,0,0,200,80Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M200,72a56,56,0,0,0-39.14,96H95.14A56,56,0,1,0,56,184H200a56,56,0,0,0,0-112ZM16,128a40,40,0,1,1,40,40A40,40,0,0,1,16,128Zm184,40a40,40,0,1,1,40-40A40,40,0,0,1,200,168Z" }, null, -1), S = [
  k,
  x
], z = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M200,72a56,56,0,0,0-39.14,96H95.14A56,56,0,1,0,56,184H200a56,56,0,0,0,0-112ZM56,168a40,40,0,1,1,40-40A40,40,0,0,1,56,168Zm144,0a40,40,0,1,1,40-40A40,40,0,0,1,200,168Zm24-40a24,24,0,1,1-24-24A24,24,0,0,1,224,128ZM80,128a24,24,0,1,1-24-24A24,24,0,0,1,80,128Z" }, null, -1), B = [
  C
], N = { key: 3 }, V = /* @__PURE__ */ o("path", { d: "M200,74a54,54,0,0,0-33.89,96H89.89A54,54,0,1,0,56,182H200a54,54,0,0,0,0-108ZM14,128a42,42,0,1,1,42,42A42,42,0,0,1,14,128Zm186,42a42,42,0,1,1,42-42A42,42,0,0,1,200,170Z" }, null, -1), b = [
  V
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M200,72a56,56,0,0,0-39.14,96H95.14A56,56,0,1,0,56,184H200a56,56,0,0,0,0-112ZM16,128a40,40,0,1,1,40,40A40,40,0,0,1,16,128Zm184,40a40,40,0,1,1,40-40A40,40,0,0,1,200,168Z" }, null, -1), W = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M200,76a52,52,0,0,0-27.66,96H83.66A52,52,0,1,0,56,180H200a52,52,0,0,0,0-104ZM12,128a44,44,0,1,1,44,44A44.05,44.05,0,0,1,12,128Zm188,44a44,44,0,1,1,44-44A44.05,44.05,0,0,1,200,172Z" }, null, -1), q = [
  j
], D = {
  name: "PhVoicemail"
}, I = /* @__PURE__ */ g({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(c) {
    const a = c, d = n("weight", "regular"), _ = n("size", "1em"), h = n("color", "currentColor"), u = n("mirrored", !1), s = i(() => a.weight ?? d), l = i(() => a.size ?? _), m = i(() => a.color ?? h), p = i(() => a.mirrored !== void 0 ? a.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, F) => (t(), e("svg", Z({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: m.value,
      transform: p.value
    }, r.$attrs), [
      A(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", y, H)) : s.value === "duotone" ? (t(), e("g", w, S)) : s.value === "fill" ? (t(), e("g", z, B)) : s.value === "light" ? (t(), e("g", N, b)) : s.value === "regular" ? (t(), e("g", E, W)) : s.value === "thin" ? (t(), e("g", $, q)) : v("", !0)
    ], 16, M));
  }
});
export {
  I as default
};
