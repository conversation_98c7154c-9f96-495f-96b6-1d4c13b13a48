import { defineComponent as g, inject as n, computed as i, openBlock as t, createElementBlock as e, mergeProps as Z, renderSlot as A, createCommentVNode as v, createElementVNode as o } from "vue";
const y = ["width", "height", "fill", "transform"], M = { key: 0 }, f = /* @__PURE__ */ o("path", { d: "M176,52H80a76,76,0,0,0,0,152h96a76,76,0,0,0,0-152Zm0,128H80A52,52,0,0,1,80,76h96a52,52,0,0,1,0,104ZM80,88a40,40,0,1,0,40,40A40,40,0,0,0,80,88Zm0,56a16,16,0,1,1,16-16A16,16,0,0,1,80,144Z" }, null, -1), H = [
  f
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M112,128A32,32,0,1,1,80,96,32,32,0,0,1,112,128Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M176,56H80a72,72,0,0,0,0,144h96a72,72,0,0,0,0-144Zm0,128H80A56,56,0,0,1,80,72h96a56,56,0,0,1,0,112ZM80,88a40,40,0,1,0,40,40A40,40,0,0,0,80,88Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,80,152Z" }, null, -1), S = [
  k,
  x
], z = { key: 2 }, C = /* @__PURE__ */ o("path", { d: "M176,56H80a72,72,0,0,0,0,144h96a72,72,0,0,0,0-144ZM80,168a40,40,0,1,1,40-40A40,40,0,0,1,80,168Z" }, null, -1), B = [
  C
], N = { key: 3 }, b = /* @__PURE__ */ o("path", { d: "M176,58H80a70,70,0,0,0,0,140h96a70,70,0,0,0,0-140Zm0,128H80A58,58,0,0,1,80,70h96a58,58,0,0,1,0,116ZM80,90a38,38,0,1,0,38,38A38,38,0,0,0,80,90Zm0,64a26,26,0,1,1,26-26A26,26,0,0,1,80,154Z" }, null, -1), E = [
  b
], P = { key: 4 }, V = /* @__PURE__ */ o("path", { d: "M176,56H80a72,72,0,0,0,0,144h96a72,72,0,0,0,0-144Zm0,128H80A56,56,0,0,1,80,72h96a56,56,0,0,1,0,112ZM80,88a40,40,0,1,0,40,40A40,40,0,0,0,80,88Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,80,152Z" }, null, -1), W = [
  V
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M176,60H80a68,68,0,0,0,0,136h96a68,68,0,0,0,0-136Zm0,128H80A60,60,0,0,1,80,68h96a60,60,0,0,1,0,120ZM80,92a36,36,0,1,0,36,36A36,36,0,0,0,80,92Zm0,64a28,28,0,1,1,28-28A28,28,0,0,1,80,156Z" }, null, -1), L = [
  j
], T = {
  name: "PhToggleLeft"
}, F = /* @__PURE__ */ g({
  ...T,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(h) {
    const a = h, d = n("weight", "regular"), c = n("size", "1em"), _ = n("color", "currentColor"), m = n("mirrored", !1), s = i(() => a.weight ?? d), l = i(() => a.size ?? c), u = i(() => a.color ?? _), p = i(() => a.mirrored !== void 0 ? a.mirrored ? "scale(-1, 1)" : void 0 : m ? "scale(-1, 1)" : void 0);
    return (r, q) => (t(), e("svg", Z({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: l.value,
      height: l.value,
      fill: u.value,
      transform: p.value
    }, r.$attrs), [
      A(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", M, H)) : s.value === "duotone" ? (t(), e("g", w, S)) : s.value === "fill" ? (t(), e("g", z, B)) : s.value === "light" ? (t(), e("g", N, E)) : s.value === "regular" ? (t(), e("g", P, W)) : s.value === "thin" ? (t(), e("g", $, L)) : v("", !0)
    ], 16, y));
  }
});
export {
  F as default
};
