import { defineComponent as g, inject as l, computed as i, openBlock as t, createElementBlock as e, mergeProps as v, renderSlot as y, createCommentVNode as Z, createElementVNode as o } from "vue";
const f = ["width", "height", "fill", "transform"], A = { key: 0 }, w = /* @__PURE__ */ o("path", { d: "M144,204a16,16,0,1,1-16-16A16,16,0,0,1,144,204Zm31.06-48.7a80,80,0,0,0-94.12,0,12,12,0,1,0,14.13,19.4,56,56,0,0,1,65.86,0,12,12,0,1,0,14.13-19.4Z" }, null, -1), M = [
  w
], k = { key: 1 }, x = /* @__PURE__ */ o("path", {
  d: "M171.68,167.88l-37.53,45.24a8,8,0,0,1-12.3,0L84.32,167.88a68,68,0,0,1,87.36,0Z",
  opacity: "0.2"
}, null, -1), S = /* @__PURE__ */ o("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91ZM128,208l-32.1-38.7a60,60,0,0,1,64.2,0Zm42.37-51.08a75.89,75.89,0,0,0-84.74,0L24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), z = [
  x,
  S
], C = { key: 2 }, B = /* @__PURE__ */ o("path", { d: "M247.89,80.91a15.93,15.93,0,0,0-6.17-10.81A186.67,186.67,0,0,0,128,32,186.67,186.67,0,0,0,14.28,70.1,15.93,15.93,0,0,0,8.11,80.91,15.65,15.65,0,0,0,11.65,92.8l104,125.43A15.93,15.93,0,0,0,128,224h0a15.93,15.93,0,0,0,12.31-5.77h0l104-125.43A15.65,15.65,0,0,0,247.89,80.91Zm-77.52,76a75.89,75.89,0,0,0-84.74,0L24.09,82.74A170.76,170.76,0,0,1,128,48,170.76,170.76,0,0,1,231.91,82.74Z" }, null, -1), L = [
  B
], N = { key: 3 }, W = /* @__PURE__ */ o("path", { d: "M138,204a10,10,0,1,1-10-10A10,10,0,0,1,138,204Zm33.53-43.85a74,74,0,0,0-87.06,0,6,6,0,0,0,7.06,9.7,62,62,0,0,1,72.94,0,6,6,0,0,0,8.38-1.32A6,6,0,0,0,171.53,160.15Z" }, null, -1), b = [
  W
], E = { key: 4 }, P = /* @__PURE__ */ o("path", { d: "M140,204a12,12,0,1,1-12-12A12,12,0,0,1,140,204Zm32.71-45.47a76.05,76.05,0,0,0-89.42,0,8,8,0,0,0,9.42,12.94,60,60,0,0,1,70.58,0,8,8,0,1,0,9.42-12.94Z" }, null, -1), V = [
  P
], $ = { key: 5 }, j = /* @__PURE__ */ o("path", { d: "M136,204a8,8,0,1,1-8-8A8,8,0,0,1,136,204Zm34.35-42.23a72,72,0,0,0-84.7,0,4,4,0,1,0,4.71,6.46,64,64,0,0,1,75.28,0,4,4,0,1,0,4.71-6.46Z" }, null, -1), q = [
  j
], D = {
  name: "PhWifiLow"
}, H = /* @__PURE__ */ g({
  ...D,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const n = d, c = l("weight", "regular"), h = l("size", "1em"), _ = l("color", "currentColor"), u = l("mirrored", !1), s = i(() => n.weight ?? c), a = i(() => n.size ?? h), p = i(() => n.color ?? _), m = i(() => n.mirrored !== void 0 ? n.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, F) => (t(), e("svg", v({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: a.value,
      height: a.value,
      fill: p.value,
      transform: m.value
    }, r.$attrs), [
      y(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", A, M)) : s.value === "duotone" ? (t(), e("g", k, z)) : s.value === "fill" ? (t(), e("g", C, L)) : s.value === "light" ? (t(), e("g", N, b)) : s.value === "regular" ? (t(), e("g", E, V)) : s.value === "thin" ? (t(), e("g", $, q)) : Z("", !0)
    ], 16, f));
  }
});
export {
  H as default
};
