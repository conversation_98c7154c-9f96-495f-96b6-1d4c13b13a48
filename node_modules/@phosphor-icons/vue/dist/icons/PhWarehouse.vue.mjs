import { defineComponent as p, inject as l, computed as n, openBlock as t, createElementBlock as e, mergeProps as v, renderSlot as m, createCommentVNode as Z, createElementVNode as o } from "vue";
const g = ["width", "height", "fill", "transform"], M = { key: 0 }, y = /* @__PURE__ */ o("path", { d: "M240,180h-4V61.13l6.51-1.39a12,12,0,1,0-5-23.47l-224,48A12,12,0,0,0,16,108a12.21,12.21,0,0,0,2.53-.26l1.48-.32V180H16a12,12,0,0,0,0,24H240a12,12,0,0,0,0-24ZM44,102.27l168-36V180H192V120a12,12,0,0,0-12-12H76a12,12,0,0,0-12,12v60H44ZM168,144H88V132h80ZM88,168h80v12H88Z" }, null, -1), f = [
  y
], w = { key: 1 }, k = /* @__PURE__ */ o("path", {
  d: "M184,128v64H72V128Z",
  opacity: "0.2"
}, null, -1), x = /* @__PURE__ */ o("path", { d: "M240,184h-8V57.9l9.67-2.08a8,8,0,1,0-3.35-15.64l-224,48A8,8,0,0,0,16,104a8.16,8.16,0,0,0,1.69-.18L24,102.47V184H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM40,99,216,61.33V184H192V128a8,8,0,0,0-8-8H72a8,8,0,0,0-8,8v56H40Zm136,53H80V136h96ZM80,168h96v16H80Z" }, null, -1), S = [
  k,
  x
], z = { key: 2 }, A = /* @__PURE__ */ o("path", { d: "M240,184h-8V57.9l9.67-2.08a8,8,0,1,0-3.35-15.64l-224,48A8,8,0,0,0,16,104a8.16,8.16,0,0,0,1.69-.18L24,102.47V184H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16Zm-56,0H72V168H184Zm0-32H72V136H184Z" }, null, -1), C = [
  A
], L = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M240,186H230V56.28l11.26-2.41a6,6,0,1,0-2.52-11.74l-224,48a6,6,0,0,0,2.52,11.74L26,100v86H16a6,6,0,0,0,0,12H240a6,6,0,0,0,0-12ZM38,97.42,218,58.85V186H190V128a6,6,0,0,0-6-6H72a6,6,0,0,0-6,6v58H38ZM178,154H78V134H178ZM78,166H178v20H78Z" }, null, -1), N = [
  B
], W = { key: 4 }, b = /* @__PURE__ */ o("path", { d: "M240,184h-8V57.9l9.67-2.08a8,8,0,1,0-3.35-15.64l-224,48A8,8,0,0,0,16,104a8.16,8.16,0,0,0,1.69-.18L24,102.47V184H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM40,99,216,61.33V184H192V128a8,8,0,0,0-8-8H72a8,8,0,0,0-8,8v56H40Zm136,53H80V136h96ZM80,168h96v16H80Z" }, null, -1), E = [
  b
], P = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M240,188H228V54.66l12.84-2.75a4,4,0,1,0-1.68-7.82l-224,48A4,4,0,0,0,16,100a4.07,4.07,0,0,0,.84-.09L28,97.52V188H16a4,4,0,0,0,0,8H240a4,4,0,0,0,0-8ZM36,95.81,220,56.38V188H188V128a4,4,0,0,0-4-4H72a4,4,0,0,0-4,4v60H36ZM180,156H76V132H180ZM76,164H180v24H76Z" }, null, -1), j = [
  $
], q = {
  name: "PhWarehouse"
}, G = /* @__PURE__ */ p({
  ...q,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(h) {
    const s = h, d = l("weight", "regular"), c = l("size", "1em"), H = l("color", "currentColor"), _ = l("mirrored", !1), a = n(() => s.weight ?? d), i = n(() => s.size ?? c), u = n(() => s.color ?? H), V = n(() => s.mirrored !== void 0 ? s.mirrored ? "scale(-1, 1)" : void 0 : _ ? "scale(-1, 1)" : void 0);
    return (r, D) => (t(), e("svg", v({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: u.value,
      transform: V.value
    }, r.$attrs), [
      m(r.$slots, "default"),
      a.value === "bold" ? (t(), e("g", M, f)) : a.value === "duotone" ? (t(), e("g", w, S)) : a.value === "fill" ? (t(), e("g", z, C)) : a.value === "light" ? (t(), e("g", L, N)) : a.value === "regular" ? (t(), e("g", W, E)) : a.value === "thin" ? (t(), e("g", P, j)) : Z("", !0)
    ], 16, g));
  }
});
export {
  G as default
};
