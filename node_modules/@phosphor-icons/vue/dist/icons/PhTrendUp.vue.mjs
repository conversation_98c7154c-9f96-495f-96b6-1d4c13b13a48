import { defineComponent as v, inject as a, computed as n, openBlock as t, createElementBlock as e, mergeProps as m, renderSlot as L, createCommentVNode as y, createElementVNode as o } from "vue";
const f = ["width", "height", "fill", "transform"], w = { key: 0 }, M = /* @__PURE__ */ o("path", { d: "M244,56v64a12,12,0,0,1-24,0V85l-75.51,75.52a12,12,0,0,1-17,0L96,129,32.49,192.49a12,12,0,0,1-17-17l72-72a12,12,0,0,1,17,0L136,135l67-67H168a12,12,0,0,1,0-24h64A12,12,0,0,1,244,56Z" }, null, -1), k = [
  M
], A = { key: 1 }, Z = /* @__PURE__ */ o("path", {
  d: "M232,56v64L168,56Z",
  opacity: "0.2"
}, null, -1), V = /* @__PURE__ */ o("path", { d: "M232,48H168a8,8,0,0,0-5.66,13.66L188.69,88,136,140.69l-34.34-34.35a8,8,0,0,0-11.32,0l-72,72a8,8,0,0,0,11.32,11.32L96,123.31l34.34,34.35a8,8,0,0,0,11.32,0L200,99.31l26.34,26.35A8,8,0,0,0,240,120V56A8,8,0,0,0,232,48Zm-8,52.69L187.31,64H224Z" }, null, -1), x = [
  Z,
  V
], H = { key: 2 }, S = /* @__PURE__ */ o("path", { d: "M240,56v64a8,8,0,0,1-13.66,5.66L200,99.31l-58.34,58.35a8,8,0,0,1-11.32,0L96,123.31,29.66,189.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0L136,140.69,188.69,88,162.34,61.66A8,8,0,0,1,168,48h64A8,8,0,0,1,240,56Z" }, null, -1), z = [
  S
], C = { key: 3 }, B = /* @__PURE__ */ o("path", { d: "M238,56v64a6,6,0,0,1-12,0V70.48l-85.76,85.76a6,6,0,0,1-8.48,0L96,120.49,28.24,188.24a6,6,0,0,1-8.48-8.48l72-72a6,6,0,0,1,8.48,0L136,143.51,217.52,62H168a6,6,0,0,1,0-12h64A6,6,0,0,1,238,56Z" }, null, -1), N = [
  B
], b = { key: 4 }, E = /* @__PURE__ */ o("path", { d: "M240,56v64a8,8,0,0,1-16,0V75.31l-82.34,82.35a8,8,0,0,1-11.32,0L96,123.31,29.66,189.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0L136,140.69,212.69,64H168a8,8,0,0,1,0-16h64A8,8,0,0,1,240,56Z" }, null, -1), P = [
  E
], W = { key: 5 }, $ = /* @__PURE__ */ o("path", { d: "M236,56v64a4,4,0,0,1-8,0V65.66l-89.17,89.17a4,4,0,0,1-5.66,0L96,117.66,26.83,186.83a4,4,0,0,1-5.66-5.66l72-72a4,4,0,0,1,5.66,0L136,146.34,222.34,60H168a4,4,0,0,1,0-8h64A4,4,0,0,1,236,56Z" }, null, -1), j = [
  $
], T = {
  name: "PhTrendUp"
}, D = /* @__PURE__ */ v({
  ...T,
  props: {
    weight: {
      type: String
    },
    size: {
      type: [String, Number]
    },
    color: {
      type: String
    },
    mirrored: {
      type: Boolean
    }
  },
  setup(d) {
    const l = d, c = a("weight", "regular"), h = a("size", "1em"), _ = a("color", "currentColor"), u = a("mirrored", !1), s = n(() => l.weight ?? c), i = n(() => l.size ?? h), p = n(() => l.color ?? _), g = n(() => l.mirrored !== void 0 ? l.mirrored ? "scale(-1, 1)" : void 0 : u ? "scale(-1, 1)" : void 0);
    return (r, U) => (t(), e("svg", m({
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 256 256",
      width: i.value,
      height: i.value,
      fill: p.value,
      transform: g.value
    }, r.$attrs), [
      L(r.$slots, "default"),
      s.value === "bold" ? (t(), e("g", w, k)) : s.value === "duotone" ? (t(), e("g", A, x)) : s.value === "fill" ? (t(), e("g", H, z)) : s.value === "light" ? (t(), e("g", C, N)) : s.value === "regular" ? (t(), e("g", b, P)) : s.value === "thin" ? (t(), e("g", W, j)) : y("", !0)
    ], 16, f));
  }
});
export {
  D as default
};
