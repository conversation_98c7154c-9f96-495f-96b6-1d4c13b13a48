# 为 Cursor AI 生成的 Markdown 提示词：构建 "我的史诗" (My Epic) 应用

## 核心指令：扮演资深 Vue.js 开发者

请扮演一位资深的 Vue.js 全栈开发专家。你的任务是为一个名为 **“我的史诗 (My Epic)”** 的 Web 应用生成一个完整的、结构良好、生产就绪的样板项目。

## 项目核心概念

“我的史诗”的核心理念是，允许用户以一种 **叙事性、决策驱动** 的方式来记录他们的生活、目标和项目，其灵感来源于电子游戏 **《博德之门3》** 中的任务日志。这并非一个简单的待办事项列表，而是一个将生活构建为一场宏大冒险的个人日记工具。

## 1. 项目结构与工程化

我需要你使用 `Vue 3` (组合式 API)、`Vite`、用于状态管理的 `Pinia` 和用于路由的 `Vue Router` 来生成完整的项目结构和代码。整个项目必须组织良好、可扩展，并遵循现代化的工程最佳实践。

* **框架:** `Vue 3` 并全面使用 `<script setup>` (组合式 API)。
* **构建工具:** `Vite`。
* **路由:** `Vue Router`，并创建一个清晰的路由结构文件。
* **状态管理:** `Pinia`，为不同的业务关注点创建模块化的 Store (例如 `quests.js` 用于任务，`settings.js` 用于用户设置)。
* **样式:** `Tailwind CSS`，请确保其配置已设置好并可直接使用。
* **代码规范:** 包含 `ESLint` 和 `Prettier` 的基础配置。
* **目录结构:** 创建一个逻辑清晰且可扩展的目录结构，示例如下：
    ```bash
    /src
    ├── assets/         # 存放字体、图片、基础样式
    ├── components/     # 可复用的 UI 组件 (QuestCard.vue, LogEntry.vue, etc.)
    ├── views/          # 顶级页面 (Dashboard.vue, QuestDetail.vue, etc.)
    ├── router/         # 路由配置 (index.js)
    ├── stores/         # Pinia Stores (quests.js, settings.js)
    ├── composables/    # 可复用的逻辑 (useDateFormatter.js)
    ├── data/           # 初始的模拟/占位数据 (mockQuests.js)
    ├── App.vue
    └── main.js
    ```

## 2. 核心特性与功能

### 任务仪表盘 (`Dashboard.vue`)

* 这是主视图，应展示所有 **进行中 (`active`)** 的任务列表。
* 任务应能通过其类型（主线、伙伴、个人、差事）在视觉上被区分开（使用图标或颜色编码）。
* 实现一个筛选系统，可以按类型显示任务（例如，“仅显示主线任务”）。
* 包含一个醒目的“开启新任务”按钮，点击后会打开一个模态框。

### 数据结构（至关重要）

* **任务对象 (`Quest`)**:
    ```javascript
    {
      id: String, // 唯一标识符
      title: String, // 任务的标题
      type: 'main' | 'companion' | 'personal' | 'errand', // 任务类型
      status: 'active' | 'completed' | 'on_hold' | 'abandoned', // 任务状态
      startDate: Date, // 开始时间戳
      endDate: Date | null, // 结束时间戳
      description: String, // 关于任务起源的简短叙述
      logEntries: Array<LogEntry> // 日志条目对象的数组
    }
    ```
* **日志条目对象 (`LogEntry`)**:
    ```javascript
    {
      id: String, // 唯一标识符
      timestamp: Date, // 该条目的时间戳
      type: 'update' | 'decision' | 'milestone' | 'info' | 'closed', // 条目类型
      content: String // 日志条目的叙述性文本
    }
    ```

### 任务详情视图 (`QuestDetail.vue`)

* 通过点击仪表盘上的 `QuestCard` 进入此页面，显示任务的标题、描述和状态。
* 主要功能是按时间顺序显示所有的 `logEntries`。
* 每个条目都应根据其 `type` 进行特殊样式化：
    * **`decision` (决策)**: 应被高亮显示（例如，使用不同的背景色或图标）。
    * **`closed` (分支关闭)**: 应略微变灰或带有删除线，以表示未选择的路径。
* 页面上应有一个“更新任务”的表单/按钮，用于向当前任务添加新的日志条目。

### 状态管理 (Pinia)

* **`quests.js` store**:
    * **state**: 一个任务对象的数组。请使用 `/data/mockQuests.js` 中的一些丰富模拟数据来初始化它。
    * **getters**: `getActiveQuests`, `getCompletedQuests`, `getQuestById(id)`。
    * **actions**: `addQuest(questData)`, `updateQuest(questId, logEntryData)`, `changeQuestStatus(questId, status)`。
* **`settings.js` store**:
    * **state**: 一个用于存放用户可配置选项的对象。

## 3. 高度可配置性 (`Settings.vue`)

这是关键需求。用户应该能够自定义他们的体验。请创建一个设置页面，包含以下选项（UI 和状态管理必须就位）：

* **任务类型:** 允许用户编辑默认任务类型（名称、颜色、图标），并能添加新的自定义任务类型。
* **日志条目类型:** 允许用户自定义日志条目类型（标签、视觉指示器）。
* **主题:** 一个能在 **“暗黑奇幻”** (默认) 和 **“明亮羊皮纸”** 主题之间切换的简单开关。通过在根 `<html>` 元素上切换一个 `dark` class 并使用 Tailwind 的 `dark:` 变体来实现。
* **数据管理:** “导出为 JSON” 和 “从 JSON 导入” 按钮 (仅需实现UI)。

## 4. UI/UX 设计

* **字体:** 标题使用衬线字体 (如 `EB Garamond` 或 `Lora`)，正文使用清晰的无衬线字体 (如 `Inter` 或 `Lato`)。
* **布局:** 一个主内容区域，带有一个用于导航的持久化侧边栏（仪表盘、已完成任务、设置）。
* **视觉效果:** 使用微妙的 CSS 渐变、盒阴影和过渡效果。当鼠标悬停在交互元素上时，添加轻微的 **“辉光”** 效果（例如，一个柔和的、有色的 `box-shadow`）。
* **图标:** 使用一个图标库，如 `phosphor-icons-vue` 或 `heroicons`。

## 最终指令

请根据以上所有规范 **生成完整的项目代码**。在逻辑复杂的地方请添加注释。模拟数据应足够详细，以使应用程序在首次运行时看起来和感觉上都是功能齐全的。请从 `package.json` 开始，然后创建所有必要的文件夹和文件。